# GO2机器人的预训练策略（神经网络模型）文件的路径
policy_path: "{LEGGED_GYM_ROOT_DIR}/deploy/pre_train/go2/policy_1.pt"

xml_path: "{LEGGED_GYM_ROOT_DIR}/resources/robots/go2_official/scene.xml"

# Total simulation time
simulation_duration: 60.0
# Simulation time step
simulation_dt: 0.002
# Controller update frequency (meets the requirement of simulation_dt * controll_decimation=0.02; 50Hz)
# 降采样因子：神经网络策略（控制器）的更新频率
control_decimation: 10

# PD控制器的每个关节的比例增益 (GO2有12个关节)
kps: [20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20]

# PD控制器的每个关节的微分增益（增加系统阻尼，减小震荡）
kds: [0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5]

# 机器人12个关节的默认初始角度（弧度）
# 顺序: FL_hip, FL_thigh, FL_calf, FR_hip, FR_thigh, FR_calf, RL_hip, RL_thigh, RL_calf, RR_hip, RR_thigh, RR_calf
default_angles: [0.1, 0.8, -1.5, -0.1, 0.8, -1.5, 0.1, 1.0, -1.5, -0.1, 1.0, -1.5]

ang_vel_scale: 0.25
dof_pos_scale: 1.0
dof_vel_scale: 0.05
action_scale: 0.25
cmd_scale: [2.0, 2.0, 0.25]
# 动作空间的维度
num_actions: 12
# 观测空间的维度
num_obs: 48

cmd_init: [0.5, 0, 0]
